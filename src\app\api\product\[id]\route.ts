import { getCurrentUser } from '@/app/actions/getCurrentUser';
import prisma from '../../../libs/prismadb';
import { NextResponse } from 'next/server';

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
	const currentUser = await getCurrentUser();

	if (!currentUser || currentUser.role !== 'ADMIN') {
		return NextResponse.error();
	}

	const product = await prisma.product.delete({
		where: { id: params.id }
	});
	return NextResponse.json(product);
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
	const currentUser = await getCurrentUser();

	if (!currentUser || currentUser.role !== 'ADMIN') {
		return NextResponse.error();
	}

	const body = await request.json();
	const { name, description, price, inStock, categoryId } = body;

	const product = await prisma.product.update({
		where: { id: params.id },
		data: { name, description, price, inStock, categoryId }
	});
	return NextResponse.json(product);
}
