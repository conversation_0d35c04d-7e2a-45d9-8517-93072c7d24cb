{"name": "ecommerce-shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "build": "next build"}, "dependencies": {"@auth/prisma-adapter": "^2.1.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.17", "@mui/x-data-grid": "^7.8.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.14.0", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.5.0", "@types/bcrypt": "^5.0.2", "@types/node": "20.12.11", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.19", "axios": "^1.7.2", "bcrypt": "^5.1.1", "chart.js": "^4.4.3", "date-fns": "^3.6.0", "eslint": "8.57.0", "eslint-config-next": "14.2.3", "exact": "^1.0.1", "firebase": "^10.12.2", "jsonwebtoken": "^9.0.2", "micro": "^10.0.1", "moment": "^2.30.1", "next": "14.2.3", "next-auth": "^4.24.7", "nodemailer": "^6.9.14", "postcss": "8.4.38", "primereact": "^10.8.2", "prisma": "^5.14.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0-rc2", "query-string": "^9.0.0", "quill": "^2.0.2", "react": "18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "slugify": "^1.6.6", "stripe": "^15.10.0", "swiper": "^11.1.14", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "3.4.3", "typescript": "5.4.5"}}