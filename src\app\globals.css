@tailwind base;
@tailwind components;
@tailwind utilities;

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}
.no-scrollbar {
  -ms-overflow-style: none; /* IE & Edge */
  scrollbar-width: none; /* Firefox */
}

.flex-g-4 {
	flex: 4;
}

.overlay-post {
	background: linear-gradient(180deg, transparent 45%, #101426);
}

.swiper-button-next,
.swiper-button-prev {
	opacity: 0;
	transition: opacity 0.3s ease;
	background-color: #16b1ff;
	border-radius: 100%;
	width: 40px !important;
	height: 40px !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.swiper:hover .swiper-button-next,
.swiper:hover .swiper-button-prev {
	opacity: 1; /* Hiển thị nút khi hover */
}

.swiper-button-next::after,
.swiper-button-prev::after {
	font-size: 18px !important;
	font-weight: bold !important;
	color: white;
}
